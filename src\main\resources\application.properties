# ==================== LOCAL MYSQL CONFIGURATION ====================

spring.application.name=SalesManagementBackend

# Direct MySQL connection (Aiven Cloud)
spring.datasource.url=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=avnadmin
spring.datasource.password=AVNS_csvuR5cZDeomoG6nxvD

# Hikari Connection Pool
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=3
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.auto-commit=false

# SSL
spring.datasource.hikari.data-source-properties.useSSL=true
spring.datasource.hikari.data-source-properties.requireSSL=true
spring.datasource.hikari.data-source-properties.verifyServerCertificate=false

# JPA / Hibernate - Spring Boot Defaults
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.defer-datasource-initialization=false
spring.jpa.open-in-view=false
# Additional properties to handle schema creation gracefully
spring.jpa.properties.hibernate.hbm2ddl.halt_on_error=false

# Server (Local)
server.port=8080
server.servlet.context-path=/
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.forward-headers-strategy=framework

# Logging
logging.level.com.hamza.salesmanagementbackend=INFO
logging.level.org.springframework.web.servlet.DispatcherServlet=WARN
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.level.org.springframework.security=WARN

# JWT (for development only)
jwt.secret=bXlTZWNyZXRLZXkxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkw
jwt.expiration=86400000

# CORS (development)
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.max-age=3600

# Static resources
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.web.resources.cache.cachecontrol.max-age=31536000

# Actuator
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.ping.enabled=true

# File Upload
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

# Security settings
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# Performance
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
